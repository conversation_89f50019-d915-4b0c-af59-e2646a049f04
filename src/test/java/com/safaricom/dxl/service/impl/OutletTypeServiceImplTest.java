package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.data.dto.OutletTypeTransferDto;
import com.safaricom.dxl.data.entities.OrganizationTypeEntity;
import com.safaricom.dxl.data.entities.OutletTypeEntity;
import com.safaricom.dxl.data.repositories.OrganizationTypeRepository;
import com.safaricom.dxl.data.repositories.OutletTypeRepository;
import com.safaricom.dxl.exception.DuplicateRecordException;
import com.safaricom.dxl.exception.ForbiddenException;
import com.safaricom.dxl.exception.InternalServerErrorException;
import com.safaricom.dxl.exception.NotFoundException;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("OutletTypeServiceImpl Unit Tests")
class OutletTypeServiceImplTest {
    @Mock
    private OutletTypeRepository repository;
    @Mock
    private OrganizationTypeRepository organizationTypeRepository;
    @Mock
    private WsResponseMapper responseMapper;
    @InjectMocks
    private OutletTypeServiceImpl service;

    private OutletTypeDto dto;
    private OutletTypeEntity entity;
    private WsResponse wsResponse;
    private Map<String, String> headers;

    @BeforeEach
    void setUp() {
        dto = OutletTypeDto.builder()
                .dealerName("dealerName")
                .description("desc")
                .organizationTypeId(1L)
                .build();
        entity = OutletTypeEntity.builder()
                .id(1L)
                .dealerName("dealerName")
                .description("desc")
                .organizationTypeId(1L)
                .created(LocalDateTime.now())
                .createdBy("creator")
                .build();
        wsResponse = new WsResponse();
        headers = new HashMap<>();
        headers.put("X-Identity", "testUser");
    }

    @Nested
    @DisplayName("create()")
    class Create {
        @Test
        @DisplayName("create_RepositoryThrowsNotFoundException_PropagatesNotFoundException")
        void create_RepositoryThrowsNotFoundException_PropagatesNotFoundException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("create_RepositoryThrowsInternalServerErrorException_ThrowsInternalServerErrorException")
        void create_RepositoryThrowsInternalServerErrorException_ThrowsInternalServerErrorException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new InternalServerErrorException("internal error", "500")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("create_RepositoryThrowsNullPointerException_ThrowsInternalServerErrorException")
        void create_RepositoryThrowsNullPointerException_ThrowsInternalServerErrorException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new NullPointerException("null error")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
        @Test
        @DisplayName("create_Success_ReturnsWsResponse")
        void create_Success_ReturnsWsResponse() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));
            StepVerifier.create(service.create(dto, headers))
                .expectNext(wsResponse)
                .verifyComplete();
            verify(repository).save(any(OutletTypeEntity.class));
        }

        @Test
        @DisplayName("create_RepositoryThrows_ThrowsInternalServerErrorException")
        void create_RepositoryThrows_ThrowsInternalServerErrorException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new RuntimeException("db error")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("create_RepositoryThrowsCheckedException_ThrowsInternalServerErrorException")
        void create_RepositoryThrowsCheckedException_ThrowsInternalServerErrorException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new Exception("checked error")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("create_OrganizationTypeNotFound_ThrowsNotFoundException")
        void create_OrganizationTypeNotFound_ThrowsNotFoundException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.empty());
            StepVerifier.create(service.create(dto, headers))
                .expectError(com.safaricom.dxl.exception.NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("create_DuplicateDealerCode_ThrowsDuplicateRecordException")
        void create_DuplicateDealerCode_ThrowsDuplicateRecordException() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.just(entity));
            StepVerifier.create(service.create(dto, headers))
                .expectError(DuplicateRecordException.class)
                .verify();
        }

        @Test
        @DisplayName("create_OrganizationTypeRepositoryThrows_ErrorHandled")
        void create_OrganizationTypeRepositoryThrows_ErrorHandled() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.error(new RuntimeException("org type error")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("create_OrganizationTypeRepositoryThrowsChainedException_ErrorPropagated")
        void create_OrganizationTypeRepositoryThrowsChainedException_ErrorPropagated() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.error(new NotFoundException("org type not found", "404")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("create_ResponseMapperThrows_ErrorHandled")
        void create_ResponseMapperThrows_ErrorHandled() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.empty());
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));
            StepVerifier.create(service.create(dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("create_FindByDealerNameThrows_ErrorHandled")
        void create_FindByDealerNameThrows_ErrorHandled() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.error(new RuntimeException("dealer name error")));

            try (org.mockito.MockedStatic<com.safaricom.dxl.utils.Shared> utilities = org.mockito.Mockito.mockStatic(com.safaricom.dxl.utils.Shared.class)) {
                utilities.when(() -> com.safaricom.dxl.utils.Shared.returnChainedException(any(Throwable.class))).thenReturn(false);

                StepVerifier.create(service.create(dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
            }
        }

        @Test
        @DisplayName("create_FindByDealerNameThrowsChainedException_ErrorPropagated")
        void create_FindByDealerNameThrowsChainedException_ErrorPropagated() {
            when(organizationTypeRepository.findById(anyLong())).thenReturn(Mono.just(
                OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build()
            ));
            when(repository.findByDealerName(anyString())).thenReturn(Mono.error(new NotFoundException("dealer not found", "404")));

            StepVerifier.create(service.create(dto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("update()")
    class Update {
        @Test
        @DisplayName("update_Success_ReturnsWsResponse")
        void update_Success_ReturnsWsResponse() {
            OrganizationTypeEntity orgEntity = OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(orgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.update(1L, dto, headers))
                .expectNext(wsResponse)
                .verifyComplete();
        }

        @Test
        @DisplayName("update_NotFound_ThrowsNotFoundException")
        void update_NotFound_ThrowsNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.empty());
            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("update_OrganizationTypeNotFound_ThrowsNotFoundException")
        void update_OrganizationTypeNotFound_ThrowsNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.empty());

            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("update_DifferentOrganizationTypeId_ThrowsForbiddenException")
        void update_DifferentOrganizationTypeId_ThrowsForbiddenException() {
            OutletTypeEntity existingEntity = OutletTypeEntity.builder()
                    .id(1L)
                    .dealerName("dealerName")
                    .description("desc")
                    .organizationTypeId(2L) // Different org type ID
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            OrganizationTypeEntity orgEntity = OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(existingEntity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(orgEntity));

            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(ForbiddenException.class)
                .verify();
        }

        @Test
        @DisplayName("update_RepositoryThrows_ThrowsInternalServerErrorException")
        void update_RepositoryThrows_ThrowsInternalServerErrorException() {
            OrganizationTypeEntity orgEntity = OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(orgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new RuntimeException("db error")));

            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("update_FindByIdThrows_ErrorHandled")
        void update_FindByIdThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.error(new RuntimeException("find error")));

            try (org.mockito.MockedStatic<com.safaricom.dxl.utils.Shared> utilities = org.mockito.Mockito.mockStatic(com.safaricom.dxl.utils.Shared.class)) {
                utilities.when(() -> com.safaricom.dxl.utils.Shared.returnChainedException(any(Throwable.class))).thenReturn(false);

                StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
            }
        }

        @Test
        @DisplayName("update_FindByIdThrowsChainedException_ErrorPropagated")
        void update_FindByIdThrowsChainedException_ErrorPropagated() {
            when(repository.findById(1L)).thenReturn(Mono.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("update_OrganizationTypeRepositoryThrows_ErrorHandled")
        void update_OrganizationTypeRepositoryThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.error(new RuntimeException("org type error")));

            try (org.mockito.MockedStatic<com.safaricom.dxl.utils.Shared> utilities = org.mockito.Mockito.mockStatic(com.safaricom.dxl.utils.Shared.class)) {
                utilities.when(() -> com.safaricom.dxl.utils.Shared.returnChainedException(any(Throwable.class))).thenReturn(false);

                StepVerifier.create(service.update(1L, dto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
            }
        }

        @Test
        @DisplayName("update_OrganizationTypeRepositoryThrowsChainedException_ErrorPropagated")
        void update_OrganizationTypeRepositoryThrowsChainedException_ErrorPropagated() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.error(new NotFoundException("org type not found", "404")));

            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("update_ResponseMapperThrows_ErrorHandled")
        void update_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeEntity orgEntity = OrganizationTypeEntity.builder()
                    .id(1L)
                    .name("orgType")
                    .description("desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(1L)).thenReturn(Mono.just(orgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.update(1L, dto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("delete()")
    class Delete {
        @Test
        @DisplayName("delete_RepositoryThrowsNotFoundException_PropagatesNotFoundException")
        void delete_RepositoryThrowsNotFoundException_PropagatesNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_RepositoryThrowsInternalServerErrorException_ThrowsInternalServerErrorException")
        void delete_RepositoryThrowsInternalServerErrorException_ThrowsInternalServerErrorException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.error(new InternalServerErrorException("internal error", "500")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_RepositoryThrowsNullPointerException_ThrowsInternalServerErrorException")
        void delete_RepositoryThrowsNullPointerException_ThrowsInternalServerErrorException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.error(new NullPointerException("null error")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
        @Test
        @DisplayName("delete_Success_ReturnsWsResponse")
        void delete_Success_ReturnsWsResponse() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.empty());
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));
            StepVerifier.create(service.delete(1L, headers))
                .expectNext(wsResponse)
                .verifyComplete();
        }

        @Test
        @DisplayName("delete_RepositoryThrows_ThrowsInternalServerErrorException")
        void delete_RepositoryThrows_ThrowsInternalServerErrorException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.error(new RuntimeException("db error")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_RepositoryThrowsCheckedException_ThrowsInternalServerErrorException")
        void delete_RepositoryThrowsCheckedException_ThrowsInternalServerErrorException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.error(new Exception("checked error")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_NotFound_ThrowsNotFoundException")
        void delete_NotFound_ThrowsNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.empty());
            StepVerifier.create(service.delete(1L, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_FindByIdThrows_ErrorHandled")
        void delete_FindByIdThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.error(new RuntimeException("find error")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_FindByIdThrowsChainedException_ErrorPropagated")
        void delete_FindByIdThrowsChainedException_ErrorPropagated() {
            when(repository.findById(1L)).thenReturn(Mono.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("delete_ResponseMapperThrows_ErrorHandled")
        void delete_ResponseMapperThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(repository.deleteById(1L)).thenReturn(Mono.empty());
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));
            StepVerifier.create(service.delete(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("getById()")
    class GetById {
        @Test
        @DisplayName("getById_Success_ReturnsWsResponse")
        void getById_Success_ReturnsWsResponse() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));
            StepVerifier.create(service.getById(1L, headers))
                .expectNext(wsResponse)
                .verifyComplete();
        }

        @Test
        @DisplayName("getById_NotFound_ThrowsNotFoundException")
        void getById_NotFound_ThrowsNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.empty());
            StepVerifier.create(service.getById(1L, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("getById_RepositoryThrows_ThrowsInternalServerErrorException")
        void getById_RepositoryThrows_ThrowsInternalServerErrorException() {
            when(repository.findById(1L)).thenReturn(Mono.error(new RuntimeException("db error")));
            StepVerifier.create(service.getById(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("getById_RepositoryThrowsChainedException_ErrorPropagated")
        void getById_RepositoryThrowsChainedException_ErrorPropagated() {
            when(repository.findById(1L)).thenReturn(Mono.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.getById(1L, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("getById_ResponseMapperThrows_ErrorHandled")
        void getById_ResponseMapperThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));
            StepVerifier.create(service.getById(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("getAll()")
    class GetAll {
        @Test
        @DisplayName("getAll_Success_ReturnsWsResponse")
        void getAll_Success_ReturnsWsResponse() {
            when(repository.findAll()).thenReturn(Flux.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));
            StepVerifier.create(service.getAll(headers))
                .expectNext(wsResponse)
                .verifyComplete();
        }

        @Test
        @DisplayName("getAll_NotFound_ThrowsNotFoundException")
        void getAll_NotFound_ThrowsNotFoundException() {
            when(repository.findAll()).thenReturn(Flux.empty());
            StepVerifier.create(service.getAll(headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("getAll_RepositoryThrows_ThrowsInternalServerErrorException")
        void getAll_RepositoryThrows_ThrowsInternalServerErrorException() {
            when(repository.findAll()).thenReturn(Flux.error(new RuntimeException("db error")));
            StepVerifier.create(service.getAll(headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("getAll_RepositoryThrowsChainedException_ErrorPropagated")
        void getAll_RepositoryThrowsChainedException_ErrorPropagated() {
            when(repository.findAll()).thenReturn(Flux.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.getAll(headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("getAll_ResponseMapperThrows_ErrorHandled")
        void getAll_ResponseMapperThrows_ErrorHandled() {
            when(repository.findAll()).thenReturn(Flux.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));
            StepVerifier.create(service.getAll(headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("getByOrganizationTypeId()")
    class GetByOrganizationTypeId {
        @Test
        @DisplayName("getByOrganizationTypeId_Success_ReturnsWsResponse")
        void getByOrganizationTypeId_Success_ReturnsWsResponse() {
            when(repository.findByOrganizationTypeId(1L)).thenReturn(Flux.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));
            StepVerifier.create(service.getByOrganizationTypeId(1L, headers))
                .expectNext(wsResponse)
                .verifyComplete();
        }

        @Test
        @DisplayName("getByOrganizationTypeId_NotFound_ThrowsNotFoundException")
        void getByOrganizationTypeId_NotFound_ThrowsNotFoundException() {
            when(repository.findByOrganizationTypeId(1L)).thenReturn(Flux.empty());
            StepVerifier.create(service.getByOrganizationTypeId(1L, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("getByOrganizationTypeId_RepositoryThrows_ThrowsInternalServerErrorException")
        void getByOrganizationTypeId_RepositoryThrows_ThrowsInternalServerErrorException() {
            when(repository.findByOrganizationTypeId(1L)).thenReturn(Flux.error(new RuntimeException("db error")));
            StepVerifier.create(service.getByOrganizationTypeId(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("getByOrganizationTypeId_RepositoryThrowsChainedException_ErrorPropagated")
        void getByOrganizationTypeId_RepositoryThrowsChainedException_ErrorPropagated() {
            when(repository.findByOrganizationTypeId(1L)).thenReturn(Flux.error(new NotFoundException("not found", "404")));
            StepVerifier.create(service.getByOrganizationTypeId(1L, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("getByOrganizationTypeId_ResponseMapperThrows_ErrorHandled")
        void getByOrganizationTypeId_ResponseMapperThrows_ErrorHandled() {
            when(repository.findByOrganizationTypeId(1L)).thenReturn(Flux.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));
            StepVerifier.create(service.getByOrganizationTypeId(1L, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
    }

    @Nested
    @DisplayName("transferOut()")
    class TransferOut {
        private OutletTypeTransferDto transferDto;

        @BeforeEach
        void setUp() {
            transferDto = OutletTypeTransferDto.builder()
                    .outletTypeId(1L)
                    .targetOrganizationTypeId(2L)
                    .build();
        }

        @Test
        @DisplayName("transferOut_Success_ReturnsWsResponse")
        void transferOut_Success_ReturnsWsResponse() {
            OrganizationTypeEntity targetOrgEntity = OrganizationTypeEntity.builder()
                    .id(2L)
                    .name("targetOrgType")
                    .description("target desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.just(targetOrgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.just(wsResponse));

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectNext(wsResponse)
                .verifyComplete();
        }

        @Test
        @DisplayName("transferOut_OutletTypeNotFound_ThrowsNotFoundException")
        void transferOut_OutletTypeNotFound_ThrowsNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.empty());

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("transferOut_TargetOrganizationTypeNotFound_ThrowsNotFoundException")
        void transferOut_TargetOrganizationTypeNotFound_ThrowsNotFoundException() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.empty());

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("transferOut_RepositoryThrows_ErrorHandled")
        void transferOut_RepositoryThrows_ErrorHandled() {
            OrganizationTypeEntity targetOrgEntity = OrganizationTypeEntity.builder()
                    .id(2L)
                    .name("targetOrgType")
                    .description("target desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.just(targetOrgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.error(new RuntimeException("db error")));

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("transferOut_OutletTypeRepositoryThrows_ErrorHandled")
        void transferOut_OutletTypeRepositoryThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.error(new RuntimeException("outlet type error")));

            try (org.mockito.MockedStatic<com.safaricom.dxl.utils.Shared> utilities = org.mockito.Mockito.mockStatic(com.safaricom.dxl.utils.Shared.class)) {
                utilities.when(() -> com.safaricom.dxl.utils.Shared.returnChainedException(any(Throwable.class))).thenReturn(false);

                StepVerifier.create(service.transferOut(transferDto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
            }
        }

        @Test
        @DisplayName("transferOut_OutletTypeRepositoryThrowsChainedException_ErrorPropagated")
        void transferOut_OutletTypeRepositoryThrowsChainedException_ErrorPropagated() {
            when(repository.findById(1L)).thenReturn(Mono.error(new NotFoundException("outlet type not found", "404")));

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("transferOut_OrganizationTypeRepositoryThrows_ErrorHandled")
        void transferOut_OrganizationTypeRepositoryThrows_ErrorHandled() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.error(new RuntimeException("org type error")));

            try (org.mockito.MockedStatic<com.safaricom.dxl.utils.Shared> utilities = org.mockito.Mockito.mockStatic(com.safaricom.dxl.utils.Shared.class)) {
                utilities.when(() -> com.safaricom.dxl.utils.Shared.returnChainedException(any(Throwable.class))).thenReturn(false);

                StepVerifier.create(service.transferOut(transferDto, headers))
                    .expectError(InternalServerErrorException.class)
                    .verify();
            }
        }

        @Test
        @DisplayName("transferOut_OrganizationTypeRepositoryThrowsChainedException_ErrorPropagated")
        void transferOut_OrganizationTypeRepositoryThrowsChainedException_ErrorPropagated() {
            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.error(new NotFoundException("org type not found", "404")));

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(NotFoundException.class)
                .verify();
        }

        @Test
        @DisplayName("transferOut_ResponseMapperThrows_ErrorHandled")
        void transferOut_ResponseMapperThrows_ErrorHandled() {
            OrganizationTypeEntity targetOrgEntity = OrganizationTypeEntity.builder()
                    .id(2L)
                    .name("targetOrgType")
                    .description("target desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.just(targetOrgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new RuntimeException("mapper error")));

            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }

        @Test
        @DisplayName("transferOut_ResponseMapperThrowsChainedException_ErrorMappedToInternalServerError")
        void transferOut_ResponseMapperThrowsChainedException_ErrorMappedToInternalServerError() {
            OrganizationTypeEntity targetOrgEntity = OrganizationTypeEntity.builder()
                    .id(2L)
                    .name("targetOrgType")
                    .description("target desc")
                    .created(LocalDateTime.now())
                    .createdBy("creator")
                    .build();

            when(repository.findById(1L)).thenReturn(Mono.just(entity));
            when(organizationTypeRepository.findById(2L)).thenReturn(Mono.just(targetOrgEntity));
            when(repository.save(any(OutletTypeEntity.class))).thenReturn(Mono.just(entity));
            when(responseMapper.setApiResponse(anyString(), any(), anyString(), anyBoolean(), anyMap())).thenReturn(Mono.error(new NotFoundException("mapper not found", "404")));

            // The transferOut method doesn't check for chained exceptions in response mapper error handling
            // It always wraps in InternalServerErrorException
            StepVerifier.create(service.transferOut(transferDto, headers))
                .expectError(InternalServerErrorException.class)
                .verify();
        }
    }
}
