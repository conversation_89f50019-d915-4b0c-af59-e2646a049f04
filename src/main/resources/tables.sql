DROP TABLE IF EXISTS user_session;
CREATE TABLE IF NOT EXISTS pp_user_on_board_logs(
    id BIGSERIAL NOT NULL,
    code VARCHAR NOT NULL,
    doc_id VARCHAR NOT NULL,
    msisdn VARCHAR NOT NULL,
    email VARCHAR NOT NULL,
    response_message VARCHAR NOT NULL,
    response_code VA<PERSON>HAR NULL,
    created_by VARCHAR NOT NULL,
    created timestamp NOT NULL,
    CONSTRAINT pp_user_on_board_logs_pkey PRIMARY KEY (id));

CREATE TABLE IF NOT EXISTS dealer.pp_org_branches(
    id BIGSERIAL NOT NULL,
    branch_code character varying NOT NULL,
    branch_name character varying NOT NULL,
    org_id integer NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_org_branches_pkey PRIMARY KEY (id));

CREATE TABLE IF NOT EXISTS dealer.pp_clusters(
    id BIGSERIAL NOT NULL,
    region character varying NOT NULL,
    territory character varying NOT NULL,
    cluster character varying NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_clusters_pkey PRIMARY KEY (id));

CREATE TABLE IF NOT EXISTS dealer.pp_organizations(
    id BIGSERIAL NOT NULL,
    code character varying NOT NULL,
    ho_store_number character varying NULL,
    org_name character varying NOT NULL,
    outlet_type character varying NOT NULL,
    org_type character varying NOT NULL,
    org_branch_ids integer[] NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_organizations_pkey PRIMARY KEY (id));

CREATE TABLE IF NOT EXISTS dealer.pp_organization_type(
    id BIGSERIAL NOT NULL,
    name character varying NOT NULL,
    description character varying NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_org_organization_type_pkey PRIMARY KEY (id),
    CONSTRAINT pp_pp_organization_type_name_unique UNIQUE (name)
    );

CREATE TABLE IF NOT EXISTS dealer.pp_outlet_type(
    id BIGSERIAL NOT NULL,
    organization_type_id BIGINT NOT NULL,
    dealer_name character varying NOT NULL,
    description character varying NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_outlet_type_pkey PRIMARY KEY (id),
    CONSTRAINT fk_organization_type FOREIGN KEY (organization_type_id) REFERENCES dealer.pp_organization_type(id)
);

CREATE TABLE IF NOT EXISTS dealer.pp_permissions(
    id character varying NOT NULL,
    permission character varying NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_permissions_pkey PRIMARY KEY (id));

CREATE TABLE IF NOT EXISTS dealer.pp_roles(
    id BIGSERIAL NOT NULL,
    category character varying NOT NULL,
    role_name character varying NOT NULL,
    permission_ids text[] NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    CONSTRAINT pp_roles_pkey PRIMARY KEY (id));

CREATE TABLE IF NOT EXISTS dealer.pp_users(
    id BIGSERIAL NOT NULL,
    id_type character varying NOT NULL,
    registration_doc_id text NOT NULL,
    registration_status character varying NOT NULL,
    registration_remarks character varying NOT NULL,
    first_name character varying NOT NULL,
    last_name text NOT NULL,
    phone text NOT NULL,
    email character varying NOT NULL,
    role character varying NOT NULL,
    clusters character varying NULL,
    territory character varying NULL,
    region character varying NULL,
    codes text[] NULL,
    is_enabled boolean NOT NULL,
    created timestamp with time zone NOT NULL,
    updated timestamp with time zone NULL,
    created_by character varying NOT NULL,
    updated_by character varying NULL,
    source_system character varying NOT NULL,
    login_time timestamp with time zone NULL,
    CONSTRAINT pp_users_pkey PRIMARY KEY (id),
    CONSTRAINT pp_users_email_unique UNIQUE (email));

CREATE TABLE IF NOT EXISTS dealer.pp_users_failed_request(
    id BIGSERIAL NOT NULL,
    user_id integer NOT NULL,
    id_type character varying NOT NULL,
    registration_doc_id text NOT NULL,
    registration_status character varying NOT NULL,
    registration_remarks character varying NOT NULL,
    first_name character varying NOT NULL,
    last_name text NOT NULL,
    phone text NOT NULL,
    email character varying NOT NULL,
    role character varying NOT NULL,
    clusters character varying NULL,
    territory character varying NULL,
    region character varying NULL,
    codes text[] NULL,
    created timestamp with time zone NOT NULL,
    created_by character varying NOT NULL,
    source_system character varying NOT NULL,
    CONSTRAINT pp_users_failed_request_pkey PRIMARY KEY (id));

--INSERT INTO pp_users (id, id_type, registration_doc_id, registration_status, registration_remarks, first_name, last_name, phone, email, role, clusters, territory, region, codes, is_enabled, created, updated, created_by, updated_by, source_system, login_time)
--    SELECT id, id_type, registration_doc_id, registration_status, registration_remarks, first_name, last_name, phone, email, role, clusters, territory, region, codes, is_enabled, created, updated, created_by, updated_by, source_system, login_time
--    FROM users;
--
--INSERT INTO pp_clusters (id, cluster, created, created_by, updated, updated_by, territory, region)
--    SELECT id, cluster, created, created_by, updated, updated_by, territory, region
--    FROM clusters;
--
--INSERT INTO pp_org_branches (id, branch_code, branch_name, org_id, created, updated, created_by, updated_by)
--    SELECT id, branch_code, branch_name, org_id, created, updated, created_by, updated_by
--    FROM org_branches;
--
--INSERT INTO pp_organizations (id, code, ho_store_number, org_name, outlet_type, org_type, org_branch_ids, created, updated, created_by, updated_by)
--    SELECT id, code, ho_store_number, org_name, outlet_type, org_type, org_branch_ids, created, updated, created_by, updated_by
--    FROM organizations;
--
--INSERT INTO pp_permissions (id, permission, created, updated, created_by, updated_by)
--    SELECT id, permission, created, updated, created_by, updated_by
--    FROM permissions;
--
--INSERT INTO pp_roles (id, category, role_name, permission_ids, created, updated, created_by, updated_by)
--    SELECT id, category, role_name, permission_ids, created, updated, created_by, updated_by
--    FROM roles;
--
--INSERT INTO pp_users_failed_request (id, user_id, id_type, registration_doc_id, registration_status, registration_remarks, first_name, last_name, phone, email, role, clusters, territory, region, codes, created, created_by, source_system)
--    SELECT id, user_id, id_type, registration_doc_id, registration_status, registration_remarks, first_name, last_name, phone, email, role, clusters, territory, region, codes, created, created_by, source_system
--    FROM users_failed_request;