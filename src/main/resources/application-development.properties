encrypt.key=4c888ec286e9b570ec48a37b2020eec3d03de2400e13vy34
spring.cloud.config.username=jKfv9w4e3e
spring.cloud.config.password=5aky53c885c10e

# Basic Auth Properties
dxl.basic.auth.users.development=ks%rrR+SJ5&CXP3j
dxl.basic.auth.username=development
dxl.basic.auth.ant-matchers=


# Wrapper Utility Properties [authentication: basic-auth, bearer-token]
dxl.wrapper.utility.authentication=basic-auth
dxl.wrapper.utility.request-authorization-enabled=false
dxl.wrapper.utility.debug-mode-enabled=true
dxl.wrapper.utility.accepted-source-systems=web-portal,partner-portal
dxl.wrapper.utility.internal-source-systems=partner-portal
dxl.wrapper.utility.external-hosts=dxl.dxluat.safaricom.co.ke
dxl.wrapper.utility.required-headers=x-source-system,x-correlation-conversationid,x-msisdn,x-app,x-messageid
dxl.wrapper.utility.header-validation=x-correlation-conversationid=^[A-Za-z0-9-]+$;x-msisdn=^(254|0)?[71]\\d{8}$;x-app=^[A-Za-z0-9-]+$;x-messageid=^\\s*\\S.*$
dxl.wrapper.utility.secured-request-methods=POST,PUT,PATCH
dxl.wrapper.utility.secured-response-methods=GET,POST,PUT,PATCH,DELETE
dxl.wrapper.utility.exempted-source-systems=partner-portal
dxl.wrapper.utility.exempted-request-uri=actuator,swagger,api-docs,swagger-ui,nonce

# Session Manager Properties
dxl.session.manager.enabled=false
dxl.session.manager.exempted-sources=web-portal,partner-portal
dxl.session.manager.domain-check-enabled=false
dxl.session.manager.service-domain=dxl
dxl.session.manager.request-quota.enabled=true
dxl.session.manager.request-quota.allocated=1000
dxl.session.manager.request-quota.interval=1
dxl.session.manager.request-quota.exempted-uris=GET:/api/v1/validate
dxl.session.manager.request-quota.message=Sorry, we have noticed you are sending too many requests. To protect your account, please try again after ${dxl.session.manager.request-quota.interval} minutes

# Response/Error Mapper Properties
dxl.response.mapper.enabled=true
dxl.response.mapper.mapping-name=Partner Portal
dxl.response.mapper.error-code-override-enabled=false
dxl.response.mapper.source=service

#Kafka Properties
dxl.kafka.enabled=false
dxl.stream.producer=kafka
dxl.kafka.consumer.enabled=false
dxl.kafka.consumer.max-poll-records=1
dxl.kafka.consumer.poll-delay=100
dxl.kafka.consumer.concurrency=3
dxl.kafka.consumer.processor=flux
dxl.kafka.consumer.group-id=foonewer
dxl.kafka.add-topic=false
dxl.kafka.topic-partitions=1
dxl.kafka.topic-replicas=2
dxl.kafka.topic=partner_portal_topic
dxl.kafka.useKeyStore=false
dxl.kafka.key-store-path=classpath:mskClientKeystore.jks
dxl.kafka.username=DXL2021
dxl.kafka.password=S@faricom202!
dxl.kafka.security-protocol=SASL_SSL
dxl.kafka.sasl-mechanism=SCRAM-SHA-512
dxl.kafka.bootstrap-servers=b-2.eu-west-1-aws-dxl-msk.3q7sej.c6.kafka.eu-west-1.amazonaws.com:9096,b-1.eu-west-1-aws-dxl-msk.3q7sej.c6.kafka.eu-west-1.amazonaws.com:9096

# Redis Properties
spring.data.redis.enabled=false
spring.data.redis.host=**************
spring.data.redis.port=6500
spring.data.redis.password=test@2020@dxl!
spring.data.redis.ssl=false
spring.data.redis.ssl.enabled=false
spring.data.redis.ssl.bundle=
# spring.data.redis.sentinel.master=
# spring.data.redis.sentinel.nodes=

# Integration Client Properties
dxl.integration.web-client-enabled=true
dxl.integration.connect-timeout=5000
dxl.integration.read-timeout=30000
dxl.integration.max-in-memory-size=0.5
dxl.integration.ssl-verifier-enabled=false
dxl.integration.key-store-type=PKCS12
dxl.integration.key-store-algorithm=SunX509
dxl.integration.key-store-path=
dxl.integration.key-store-password=
dxl.integration.trust-store-path=
dxl.integration.trust-store-password=

#Dxl Starter Properties
dxl.starter.swagger-title=ms-channels-partner-hub-reg
dxl.starter.swagger-description=Partner Portal
dxl.starter.developer-name=Daniel Kinyanjui
dxl.starter.developer-email=<EMAIL>
dxl.starter.bearer-key=J17LXQ6bZ2L9U8Ag3gcIxJyWpH5BqRBk1FRR4jwhKc0BitPEAGSvHme0vgiEfBZfMqzl8y23jbumGpVsKCA4RQ==

# Logging properties
logging.level.org.springframework.web=INFO
logging.level.org.springframework.ws=ERROR
logging.level.org.springframework.cloud=ERROR
logging.level.org.springframework.web.servlet=ERROR
logging.level.org.apache.kafka=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} | Severity=%-5p | MicroService=${spring.service.name} | %m %n

# Response Mapper Error Code Override
dxl.response.mapper.error-code-override[0]=FETCH_RESPONSES;error-code-1;new-error-code-1
dxl.response.mapper.error-code-override[1]=FETCH_RESPONSES;error-code-2;new-error-code-2

#Service Response/Error Mapping
dxl.response.mapper.messages.200={"status":200, "response":"Success", "message":"Request executed successfully"}
dxl.response.mapper.messages.201={"status":200, "response":"Created", "message":"Record created successfully"}
dxl.response.mapper.messages.400={"status":400, "response":"Bad Request", "message":"Bad Request. Check message format"}
dxl.response.mapper.messages.401={"status":401, "response":"Not authorized", "message":"Sorry, you are not authorized to access the resource"}
dxl.response.mapper.messages.404={"status":404, "response":"Not Found", "message":"Sorry, resource not found"}
dxl.response.mapper.messages.465={"status":465, "response":"Validation Failed", "message":"Sorry, validation failed"}
dxl.response.mapper.messages.CE_ERR001={"status":409, "response":"Conflict", "message":"Record already exist"}
dxl.response.mapper.messages.CE_ERR002={"status":403, "response":"Forbidden", "message":"Action not permitted"}
dxl.response.mapper.messages.500={"status":500, "response":"Internal Server Error", "message":"An internal server error was encountered while processing request"}
dxl.response.mapper.messages.501={"status":501, "response":"Service Integration Error", "message":"An error was encountered while trying to establish an integration request"}
dxl.response.mapper.messages.DB_ERR001={"status":500, "response":"DB Write Error", "message":"An error was encountered during a database write operation. Try again later."}
dxl.response.mapper.messages.DB_ERR002={"status":500, "response":"DB Read Error", "message":"An error was encountered during a database read operation. Try again later."}
dxl.response.mapper.messages.DB_ERR003={"status":500, "response":"DB Update Error", "message":"An error was encountered during a database update operation. Try again later."}
dxl.response.mapper.messages.DB_ERR004={"status":500, "response":"DB Delete Error", "message":"An error was encountered during a database delete operation. Try again later."}


# KMS
dxl.ms.kms-key-id=2ff4572f-995e-4894-9a16-816154f252c1

# R2dbc Database Properties
dxl.r2dbc.host=GTOQJy3j+RWmc1A85PKybw==
dxl.r2dbc.port=UxpM5PE9yxECRXdtJlxVZg==
dxl.r2dbc.database=XrlyqZnYlW4Rx1HtZeusfw==
dxl.r2dbc.schema=XrlyqZnYlW4Rx1HtZeusfw==
dxl.r2dbc.username=nSijcMbJJNv7pCcFPAjHz8DX/9nWT+N9+OiMsJ1CdeQ=
dxl.r2dbc.password=QisyHknLj2aL8hX09SG9V3y3fHjf3y5a9UJVlqcZBdQ=

# R2DBC Pooling pooling
dxl.r2dbc.pool-initial-size=2
dxl.r2dbc.pool-max-size=5
dxl.r2dbc.pool-max-idle-time=3
dxl.r2dbc.pool-validation-query=SELECT 1

# Encryption
dxl.ms.key=lXm/z+LnRnzOJiIP8ChTzCY59MOOfF4YpKzHsqEgpPA=
dxl.ms.initialization-vector=iJQET+xC+xVFRGFlf2vS5A==

dxl.ms.sub-reg-url=https://api.dxlapi.safaricom.co.ke/dxlzr/subscriber/v1/customer/information
dxl.ms.sub-agent-url=http://*************:10021/v1
dxl.ms.sub-agent-auth=Basic c2ltc3dhcDoqVElMTCNTaW1Td2FwIQ==

# Tibco IPRS
# dxl.ms.government-api-url=http://***********/contract/sfc/registration/v1/iprs/validate
# DMZ IPRS
dxl.ms.government-api-url=https://***********:39445/contract/sfc/registration/v1/iprs/validate
dxl.ms.government-api-auth=Basic U3VicmVnQDJPISFzOktsYXNkaHk4OSM=

dxl.ms.token-url=https://authdxl.safaricom.co.ke/oauth2/token?grant_type=client_credentials
dxl.ms.token-auth=Basic MmQyNGE0c3Bob2Z1aGphdjY0aHJsMm8xdmM6YXQ3dWI4b2xzMXVwZGRvbzNwbmpvdnZkMGpncXEyYzVnNnMxbWxqbm9sYzBzMTcwdnNo

dxl.ms.x-message-id=asasasas|Asasasasaadw|aass22|2asasas
dxl.ms.x-api-key=a50d6e0ad7794ccd8c6f7eae88de499f1
dxl.ms.x-device-id=cdvasc0bvnbda3

dxl.ms.roles-under-super-user=Super User,RTC Partner Support,Director,Administrator,TBL,BO AML,BO Admin,BO Legal,L300

dxl.ms.trans-token-api=TOKEN_API,END_POINT,TOKEN_API
dxl.ms.trans-identity-registration-failed=IDENTITY_REGISTRATION_FAILED
dxl.ms.country-code=ke
dxl.ms.division=DE
dxl.ms.operator=mysafaricom
dxl.ms.api-key=x-api-key
dxl.ms.source-system=web-portal
dxl.ms.partner-code-error=Kindly enter a valid dealer code/short code. Sample Format: D-XX00 or 0000000.
dxl.ms.kyc-approval-msg=Your KYC is not approved. Kindly complete KYC document submission to progress. If already submitted, kindly contact support on - <EMAIL>
dxl.ms.kyc-director-error-msg=No Director documents.
dxl.ms.kyc-missing-errer-msg=KYC missing or not approved.

dxl.ms.gk-iprs=false

dxl.ms.basic-auth=Basic ZGV2ZWxvcG1lbnQ6a3MlcnJSK1NKNSZDWFAzag==
dxl.ms.partner-source-system=partner-portal

dxl.ms.rate-limit-threshold=5
dxl.ms.expiration-time-in-minutes=1

x-devicetoken=rtyuiop
x-version=1
x-device-info=ghjklhjshfhjdsa

dxl.ms.invalid-code-msg=Invalid Dealer/HO Code. Kindly enter the correct code to continue with registration.
dxl.ms.user-already-exists-msg=Email already exists for this dealer code. Click on log in to access the portal.
dxl.ms.document-type-msg=Registration Document type not allowed. Please contact <NAME_EMAIL>.

dxl.di.update-otp-url=http://ms-identity-update-profile-service:8080/api/v1/users/update-userDetails

dxl.ms.ttl=1440
dxl.ms.role-not-allowed-to-query-other-dealers=Director,Administrator
dxl.ms.enable-streaming=true
dxl.ms.enable-session-and-code-validation=false

# Digital Identity Token Validation
dxl.di.sso-enabled=true
dxl.di.x-message-id=asasasas|Asasasasaadw|aass22|2asasas
dxl.di.ttl=60
dxl.di.partner-source-system=identity
dxl.di.source-system=web-portal
dxl.di.basic-auth=Basic ZGV2ZWxvcG1lbnQ6a3MlcnJSK1NKNSZDWFAzag==
#dxl.di.identity-reg-sts-url=http://ms-identity-profile-service:8080/api/v1/identity/customer/registration/enterprise
dxl.di.identity-reg-sts-url=http://ms-identity-boss-registration-service.dxl.svc.cluster.local:8080/boss/api/v1/admin/users
dxl.di.validate-token-sts-url=http://ms-identity-user-details-service:8080/api/v1/users/user/details
dxl.di.continue-path=https://www.uat-partnerhub.safaricom.co.ke/sign-in
dxl.di.client=enterprise
dxl.di.service=admin-cli
dxl.di.clientId=f3b10b79-b77e-4041-9db9-3679627c269e
dxl.di.realmId=65f096c544caf60ff5f74e3c
dxl.di.email-confirmation=CUSTOMIZE
dxl.di.x-api-key=OWqwCWWp1w9FlWBWUOnOv5F5hLmWDdQs7rvS9IsS
dxl.di.token-url=https://sandbox-dxl.auth.eu-west-1.amazoncognito.com/oauth2/token?grant_type=client_credentials
dxl.di.token-auth=Basic NXRhMGpkcDh1NjVwamswb3FkYzBidmxiamw6MWhhY29ja3QyYWlnaGducjVlZXM0aTYwbHBiamw4dnE2MGU3M29sNjVqYm9oOGdyaTByNQ==
dxl.di.validate-token-url=https://api.dxlsandbox.safaricom.co.ke/identity/idp/v1/users/user/get-details

# dxl.ms.no-director-error-msg=The ID provided does not exists as a director. Please enter a valid ID. If you have done change of ownership click on this link: https://forms.office.com/r/Eu0xzhYnSC
dxl.ms.no-director-error-msg=The ID provided does not exists as a director. Please enter a valid ID.SAFARICOM PLC

#=====================================


logging.level.org.springframework.cloud.config=INFO
#Dxl Starter Properties
dxl.starter.static-iv=VoQgEGXj0aJkCWR2
dxl.starter.app-version=1.0.0
dxl.starter.msisdn-prefix=254
dxl.starter.msisdn-length=9
dxl.starter.proxy-enabled=false
dxl.starter.proxy-host=proxy2
dxl.starter.proxy-port=8080
dxl.starter.hashing-rounds=5

# Basic Auth Properties
dxl.basic.auth.enabled=true
#Digital Identity settings
dxl.di.auth.key=SAF_DI:identity_authorization:
dxl.di.auth.role-key=SAF_DI:identity_roles:
dxl.di.auth.grpKey=SAF_DI:identity_groups:
dxl.di.auth.jwk-uri-conn-timeout=2000
dxl.di.auth.jwk-uri-read-timeout=2000


# Wrapper Utility Properties
dxl.wrapper.utility.response-headers.X-Frame-Options=deny
dxl.wrapper.utility.response-headers.X-Content-Type-Options=nosniff
dxl.wrapper.utility.response-headers.Content-Security-Policy=default-src 'none'
dxl.wrapper.utility.sensitive-headers=authorization,x-messageid,content-length,accept,connection,accept-encoding,x-devicetoken,x-source-countrycode,origin,\
x-source-division,x-source-countrycode,x-api-key,x-envoy-peer-metadata,x-amzn-apigateway-api-id,x-session-token,x-envoy-peer-metadata-id,x-envoy-decorator-operation

# Session Manager Properties
dxl.session.manager.source-prefix=default:Sess-,mycounty-android:MyCounty-,mycounty-mini-app:MiniApp-,mysafaricom-android:MySaf-,mysafaricom-ios:MySaf-,home:Home-,trace:TraceApp-,web-portal:web-
dxl.session.manager.required-headers=x-deviceid,x-devicetoken,x-version
dxl.session.manager.header-validation=x-deviceid=^[A-Za-z0-9-]+$;x-devicetoken=^[A-Za-z0-9-]+$;x-version=^\\s*\\S.*$
dxl.session.manager.service-domain-whitelist=dxl-service-domain-whitelist
dxl.session.manager.service-domain-code=209

# Response Mapper Properties
dxl.response.mapper.starter-mapping-name=STARTER
dxl.response.mapper.url=http://ms-messages-mapper-service:8080/api/v1/messages
dxl.response.mapper.default-success-code=200
dxl.response.mapper.default-response-code=400
dxl.response.mapper.default-success-message=Operation successful
dxl.response.mapper.default-response-message=Request failed, please try again later
dxl.response.mapper.default-error-params=error,timestamp,message,requestId,path,exception,errors

# Actuator properties
management.endpoints.web.enabled-by-default=false
management.endpoints.web.exposure.include=refresh,health,prometheus,openapi

spring.mvc.throw-exception-if-no-handler-found=true
spring.mvc.static-path-pattern=/swagger*

health.config.enabled=false
health.config.time-to-live=100000000


# SASL_PLAINTEXT, SASL, SSL, SASL_SSL

# PLAIN, SCRAM-SHA-256, SCRAM-SHA-512

dxl.kafka.producer.attempts=3
dxl.kafka.producer.timeout=10000
dxl.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
dxl.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer

dxl.kafka.consumer.auto-offset-reset=earliest
#Possible values: stream, flux
dxl.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
dxl.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer

spring.kafka.enable-auto-commit=true
spring.kafka.enable-batch-listener=true
spring.kafka.max.partition.fetch.bytes=10485760
spring.kafka.auth-exception-retry-ms=5000

#Service responses/Error Mapping
dxl.response.mapper.messages.452={"status":452, "response":"Header and Body MSISDN mismatch", "message":"Sorry, request could not be processed"}
dxl.response.mapper.messages.422={"status":422, "response":"Bad Gateway", "message":"Service is temporarily unavailable"}
dxl.response.mapper.messages.408={"status":408, "response":"Connection Timeout", "message":"Request failed, please try again later"}
dxl.response.mapper.messages.502={"status":502, "response":"Bad Gateway", "message":"Service is temporarily unavailable"}
dxl.response.mapper.messages.CRE500002={"status":502, "response":"Bad Gateway", "message":"Service is temporarily unavailable"}
dxl.response.mapper.messages.CRW100050={"status":400, "response":"Missing/Invalid headers", "message":"Missing/Invalid headers"}
dxl.response.mapper.messages.CRW100051={"status":404, "response":"Not found", "message":"Sorry, requested resource could not be found"}
dxl.response.mapper.messages.CRW100052={"status":400, "response":"Invalid payload", "message":"Request failed. Please try again later"}
dxl.response.mapper.messages.CRW100053={"status":400, "response":"Invalid payload", "message":"Request failed. Please try again later"}
dxl.response.mapper.messages.CRE100054={"status":500, "response":"Internal Server Error", "message":"Request failed, please try again later"}
dxl.response.mapper.messages.CRW100055={"status":404, "response":"Method not allowed", "message":"Request failed. Please try again later"}
dxl.response.mapper.messages.CRW100057={"status":400, "response":"Bad Request", "message":"Sorry, request could not be processed"}
dxl.response.mapper.messages.CRW100058={"status":400, "response":"Bad Request", "message":"Sorry, the resource is being used by another entity"}
dxl.response.mapper.messages.CRW100061={"status":400, "response":"Validation Failed", "message":"Sorry, request could not be processed. Failed validation"}
dxl.response.mapper.messages.CRW100056={"status":404, "response":"No handler", "message":"Sorry, requested resource path could not be found"}

