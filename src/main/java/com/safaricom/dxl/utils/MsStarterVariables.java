package com.safaricom.dxl.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.safaricom.dxl.data.dto.UserDto;
import com.safaricom.dxl.data.entities.ClustersEntity;
import com.safaricom.dxl.data.entities.OrgBranchEntity;
import com.safaricom.dxl.data.entities.OrgEntity;
import com.safaricom.dxl.data.entities.UsersEntity;
import com.safaricom.dxl.data.enums.RegistrationDocumentType;
import com.safaricom.dxl.data.enums.RegistrationStatus;
import com.safaricom.dxl.data.pojos.ClusterResponse;
import com.safaricom.dxl.data.pojos.IprsErrorMapping;
import com.safaricom.dxl.data.pojos.OrgBranchResponse;
import com.safaricom.dxl.data.pojos.Token;
import de.huxhorn.sulky.ulid.ULID;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.safaricom.dxl.data.enums.RegistrationDocumentType.*;
import static com.safaricom.dxl.data.enums.RegistrationStatus.*;
import static com.safaricom.dxl.webflux.starter.logging.WsLogManager.starterError;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

public class MsStarterVariables {

    MsStarterVariables() {
    }

    public static final String SERVICE_NAME = "ms-channels-partner-hub-reg";
    public static final String ERR_SUCCESS = "200";
    public static final String ERR_CREATED = "201";
    public static final String  ERR_VALIDATION = "465";
    public static final String ERR_BAD_REQUEST = "400";
    public static final String ERR_FORBIDDEN = "403";
    public static final String ERR_NOT_FOUND = "404";
    public static final String ERR_CONFLICT = "CE_ERR001";
    public static final String ERR_NOT_PERMITTED = "CE_ERR002";
    public static final String ERR_DB_WRITE = "DB_ERR001";
    public static final String ERR_DB_READ = "DB_ERR002";
    public static final String ERR_DB_UPDATE = "DB_ERR003";
    public static final String ERR_DB_DELETE = "DB_ERR004";
    public static final String TRANS_USER_TOKEN_VALIDATION = "USER_TOKEN_VALIDATION";
    public static final String TRANS_SUB_REG_VALIDATION = "SUB_REG_VALIDATION";
    public static final String TRANS_IPRS_VALIDATION = "IPRS_VALIDATION";
    public static final String TRANS_USER_CREATION = "CREATE_USER,CREATE_USER,POSTGRES_DB";
    public static final String TRANS_USER_UPDATE = "UPDATE_USER,UPDATE_USER,POSTGRES_DB";
    public static final String TRANS_USER_FETCH = "FETCH_USER,FETCH_USER,POSTGRES_DB";
    public static final String TRANS_ROLE_CREATION = "CREATE_ROLE,CREATE_ROLE,POSTGRES_DB";
    public static final String TRANS_ROLE_FETCH = "FETCH_ROLE,FETCH_ROLE,POSTGRES_DB";
    public static final String TRANS_CLUSTER_CREATION = "CREATE_CLUSTER,CREATE_CLUSTER,POSTGRES_DB";
    public static final String TRANS_CLUSTER_FETCH = "FETCH_CLUSTER,FETCH_CLUSTER,POSTGRES_DB";

    public static final String TRANS_POST_ORG_CREATION = "POST_CREATE_ORG,POST_CREATE_ORG,POSTGRES_DB";
    public static final String TRANS_PUT_ORG_UPDATE = "PUT_UPDATE_ORG,PUT_UPDATE_ORG,POSTGRES_DB";
    public static final String TRANS_DEL_ORG_DELETE = "DELETE_ORG,DELETE_ORG,POSTGRES_DB";
    public static final String TRANS_GET_ORG = "GET_ORG,GET_ORG,POSTGRES_DB";
    public static final String TRANS_POST_OUTLET_CREATION = "POST_CREATE_OUTLET,POST_CREATE_OUTLET,POSTGRES_DB";
    public static final String TRANS_PUT_OUTLET_UPDATE = "PUT_UPDATE_OUTLET,PUT_UPDATE_OUTLET,POSTGRES_DB";
    public static final String TRANS_DEL_OUTLET_DELETE = "DELETE_OUTLET,DELETE_OUTLET,POSTGRES_DB";
    public static final String TRANS_GET_OUTLET = "GET_OUTLET,GET_OUTLET,POSTGRES_DB";

    public static final String TRANS_FETCH_PERMISSION = "CREATE_PERMISSION,CREATE_PERMISSION,POSTGRES_DB";
    public static final String TRANS_PERMISSION_CREATION = "CREATE_PERMISSION,CREATE_PERMISSION,POSTGRES_DB";
    public static final String TRANS_FETCH_ORG = "FETCH_ORG,FETCH_ORG,POSTGRES_DB";
    public static final String TRANS_BRANCH_CREATION = "CREATE_BRANCH,CREATE_BRANCH,POSTGRES_DB";
    public static final String TRANS_BRANCH_FETCH = "FETCH_BRANCH,FETCH_BRANCH,POSTGRES_DB";
    public static final String TRANS_USER_LOGOUT = "USER_LOGOUT,USER_LOGOUT,SSO";
    public static final String TRANS_TOKEN_API = "TOKEN_API, END_POINT, TOKEN_API";
    public static final String TRANS_IDENTITY_REGISTRATION_FAILED = "IDENTITY_REGISTRATION_FAILED";
    public static final String NO_DIRECTOR_ERROR_MSG = "The ID provided does not exist as a director. Kindly enter a valid Director’s ID number";
    public static final String VALUE = "value";
    public static final String EMAIL = "email";
    public static final String X_TOKEN = "x-token";
    public static final String AGENT = "Agent";
    public static final String ACTIVE = "active";
    public static final String CREATED = "created";
    public static final String INVALID_ROLE = "Invalid role";
    public static final String APPROVED = "APPROVED";
    public static final String DIRECTOR = "Director";
    public static final List<RegistrationStatus> ALLOWED_SSO_REGISTRATION = List.of(SUB_REG_MATCH, IPRS_REG_MATCH, IPRS_EXEMPTED);
    public static final List<String> ROLES_WITH_ORGANIZATION = List.of(DIRECTOR, "Administrator");
    public static final List<RegistrationDocumentType> IPRS_EXEMPTED_ID_TYPES = List.of(DIPLOMATIC_PASSPORT, FOREIGN_PASSPORT);
    public static final String EMAIL_VALIDATION_REGEX = "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$";
    public static final String NAME_VALIDATION_REGEX = "^[\\w\\s\\.&]{1,100}$";
    public static String getRefId(){
        return UUID.randomUUID().toString();
    }
    public static String getTimestamp(){
        return new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
    }
    public static String formatMsisdn(String msisdn) {
        return msisdn.length() > 9 ? msisdn.substring(msisdn.length() - 9) : msisdn;
    }
    public static final String HO_REGEX = "^\\d{6,8}$";
    public static String stripString(String x){
        return x == null ? "" : x.replace("\u0000", "");
    }

    public static Object getToken(Map<String, String> headers, String o) {
        ObjectMapper objectMapper = new ObjectMapper();
        Token token;
        try {
            token = objectMapper.readValue(o, Token.class);
        } catch (IOException e) {
            starterError(headers.get(X_CONVERSATION_ID), TRANS_TOKEN_API, "N/A", "Error : " + e.getMessage());
            return null;
        }
        return token.getAccess_token();
    }

    public static void setCluster(UserDto dto, UsersEntity usersEntity) {
        usersEntity.setRegion(null);
        usersEntity.setTerritory(null);
        usersEntity.setClusters(null);
        if (dto.getRegion() != null && !dto.getRegion().isEmpty()) {
            usersEntity.setRegion(dto.getRegion());
        } else if (dto.getTerritory() != null && !dto.getTerritory().isEmpty()) {
            usersEntity.setTerritory(dto.getTerritory());
        } else if (dto.getCluster() != null && !dto.getCluster().isEmpty()){
            usersEntity.setClusters(dto.getCluster());
        }
    }

    public static Map<String, String> maskHeaders(Map<String, String> headers){
        if (headers.get(X_IDENTITY) != null && !headers.get(X_IDENTITY).isEmpty() &&  regexValidation(headers.get(X_IDENTITY), null)){
            headers.put(X_IDENTITY, maskMsisdn(headers.get(X_IDENTITY)));
        }
        headers.put(X_MSISDN, maskMsisdn(headers.get(X_MSISDN)));
        return headers;
    }

    private static String maskMsisdn(String msisdn) {
        StringBuilder sb = new StringBuilder(formatMsisdn(msisdn));
        sb.replace(3,6, "***");
        return sb.toString();
    }

    public static boolean regexValidation(String text, String pattern) {
        String regex = pattern == null ? "^(254|0)?[71]\\d{8}$" : pattern;
        if (text == null || text.isEmpty()) return false;
        Pattern p = Pattern.compile(regex);
        Matcher matcher = p.matcher(text);
        return matcher.matches();
    }

    public static List<ClusterResponse> getClusterResponse(List<ClustersEntity> clustersEntities) {
        List<ClusterResponse> clusterResponseList = new ArrayList<>();
        clustersEntities.forEach(clustersEntity -> clusterResponseList.add(clustersEntity.toClusterResponse()));
        return clusterResponseList;
    }

    public static List<OrgBranchResponse> getBranchResponse(List<OrgBranchEntity> orgBranchEntities) {
        List<OrgBranchResponse> orgBranchResponses = new ArrayList<>();
        orgBranchEntities.forEach(orgBranch -> orgBranchResponses.add(orgBranch.toBranchResponse()));
        return orgBranchResponses;
    }

    public static String generateId(String value) {
        return value != null ? String.valueOf(UUID.nameUUIDFromBytes(value.getBytes())) : null;
    }

    public static String generateId(int subString) {
        String randomLetters = RandomStringUtils.randomAscii(3);
        long timestamp = Date.from(new Date().toInstant().atZone(ZoneId.of("Africa/Nairobi")).toInstant()).getTime();
        String both = String.valueOf(timestamp).concat(randomLetters);
        return ULID.fromBytes(both.getBytes()).toString().substring(subString);
    }

    public static void setStatus(Map<String, String> headers, UserDto dto, IprsErrorMapping iprsStats, UsersEntity newUser) {
        setCluster(dto, newUser);
        newUser.setRegistrationStatus(iprsStats.getStatus());
        newUser.setRegistrationRemarks(iprsStats.getMessage());
        newUser.setCreatedBy(headers.get(X_IDENTITY));
        newUser.setSourceSystem(headers.get(X_SOURCE_SYSTEM));
        newUser.setEnabled(ALLOWED_SSO_REGISTRATION.contains(newUser.getRegistrationStatus()));
    }

    public static PageRequest getListSize(int pageNo, int pageSize, Sort sort) {
        if (pageSize < 1) pageSize = 1;
        pageNo = pageNo <= 0 ? 0 : pageNo - 1;
        return PageRequest.of(pageNo, pageSize, sort);
    }

    public static int pageNumber(int pageNo) {
        pageNo = pageNo <= 0 ? 0 : pageNo - 1;
        return pageNo;
    }

    public static void extracted(OrgEntity orgEntity, OrgBranchEntity orgBranch) {
        if (orgBranch.getId() != null && orgEntity.getOrgBranchIds() != null)
            orgEntity.getOrgBranchIds().add(orgBranch.getId());
        else {
            Set<Long> branchesId = new HashSet<>();
            branchesId.add(orgBranch.getId());
            orgEntity.setOrgBranchIds(branchesId);
        }
    }

    public static LocalDateTime localDateTime(){
        return new Date().toInstant().atZone(ZoneId.of("Africa/Nairobi")).toLocalDateTime();
    }

    public static RegistrationDocumentType getPartnerIdType(String idType) {
        RegistrationDocumentType partnerDocType;
        switch (idType.toLowerCase()) {
            case "national id card","national id":
                partnerDocType = KENYAN_ID;
                break;
            case "alien id":
                partnerDocType = KENYAN_ALIEN_ID;
                break;
            case "military id (kenyan military)","military id card":
                partnerDocType = KENYAN_MILITARY_ID;
                break;
            case "diplomatic id":
                partnerDocType = DIPLOMATIC_PASSPORT;
                break;
            case "foreign passport":
                partnerDocType = FOREIGN_PASSPORT;
                break;
            case "passport":
                partnerDocType = KENYAN_PASSPORT;
                break;
            case "refugee id":
                partnerDocType = REFUGEE_ID;
                break;
            default:
                partnerDocType = UNKNOWN;
        }
        return partnerDocType;
    }
}
