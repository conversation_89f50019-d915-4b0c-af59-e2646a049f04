package com.safaricom.dxl.controller;

import com.safaricom.dxl.data.dto.OrganizationTypeDto;
import com.safaricom.dxl.service.OrganizationTypeService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * REST controller for managing Organization Types.
 * Provides reactive CRUD endpoints for organization type resources.
 */
@RestController
@RequestMapping("/api/organization-types")
@RequiredArgsConstructor
@Validated
public class OrganizationTypeController {
    private final OrganizationTypeService organizationTypeService;

    /**
     * Create a new OrganizationType.
     *
     * @param dto Mono of OrganizationTypeDto (validated)
     * @return Mono emitting ResponseEntity with OrganizationTypeResponse
     */
    @PostMapping("/")
    public Mono<WsResponse> create(@Valid @RequestBody final OrganizationTypeDto dto, @RequestHeader Map<String, String> headers) {
        return organizationTypeService.create(dto, headers);
    }

    /**
     * Update an existing OrganizationType by id.
     *
     * @param organizationTypeId  OrganizationType id
     * @param dto Mono of OrganizationTypeDto (validated)
     * @return Mono emitting ResponseEntity with OrganizationTypeResponse
     */
    @PutMapping("/{organizationTypeId}")
    public Mono<WsResponse> update(@PathVariable final Long organizationTypeId,
                                   @Valid @RequestBody final OrganizationTypeDto dto, @RequestHeader Map<String, String> headers) {
        return organizationTypeService.update(organizationTypeId, dto, headers);
    }

    /**
     * Delete an OrganizationType by id.
     *
     * @param organizationTypeId OrganizationType id
     * @return Mono emitting ResponseEntity<Void>
     */
    @DeleteMapping("/{organizationTypeId}")
    public Mono<WsResponse> delete(@PathVariable final Long organizationTypeId, @RequestHeader Map<String, String> headers) {
        return organizationTypeService.delete(organizationTypeId, headers);
    }

    /**
     * Get an OrganizationType by id.
     *
     * @param organizationTypeId OrganizationType id
     * @return Mono emitting ResponseEntity with OrganizationTypeResponse
     */
    @GetMapping("/{organizationTypeId}")
    public Mono<WsResponse> getById(@PathVariable final Long organizationTypeId, @RequestHeader Map<String, String> headers) {
        return organizationTypeService.getById(organizationTypeId, headers);
    }

    /**
     * Get all OrganizationTypes.
     *
     * @return Flux emitting OrganizationTypeResponse
     */
    @GetMapping("/")
    public Mono<WsResponse> getAll(@RequestHeader Map<String, String> headers) {
        return organizationTypeService.getAll(headers);
    }

    /**
     * Get all OrganizationTypes with their associated OutletTypes.
     *
     * @return Flux emitting OrganizationTypeWithOutletsResponse
     */
    @GetMapping("/with-outlets")
    public Mono<WsResponse> getAllWithOutlets(@RequestHeader Map<String, String> headers) {
        return organizationTypeService.getAllWithOutlets(headers);
    }
}
