package com.safaricom.dxl.service.impl;

import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.data.dto.OutletTypeTransferDto;
import com.safaricom.dxl.data.entities.OutletTypeEntity;
import com.safaricom.dxl.data.pojos.OutletTypeResponse;
import com.safaricom.dxl.data.repositories.OrganizationTypeRepository;
import com.safaricom.dxl.data.repositories.OutletTypeRepository;
import com.safaricom.dxl.exception.DuplicateRecordException;
import com.safaricom.dxl.exception.ForbiddenException;
import com.safaricom.dxl.exception.InternalServerErrorException;
import com.safaricom.dxl.exception.NotFoundException;
import com.safaricom.dxl.mapper.LogMapper;
import com.safaricom.dxl.service.OutletTypeService;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import com.safaricom.dxl.webflux.starter.service.WsResponseMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

import static com.safaricom.dxl.utils.MsStarterVariables.*;
import static com.safaricom.dxl.utils.Shared.returnChainedException;
import static com.safaricom.dxl.webflux.starter.utils.WsStarterVariables.*;

@Service
@RequiredArgsConstructor
public class OutletTypeServiceImpl implements OutletTypeService {
    public static final String NOT_FOUND = " Not Found";
    private static final Logger log = LoggerFactory.getLogger(OutletTypeServiceImpl.class);
    private final OutletTypeRepository outletTypeRepository;
    private final OrganizationTypeRepository organizationTypeRepository;
    private final WsResponseMapper responseMapper;

    @Override
    public Mono<WsResponse> create(final OutletTypeDto dto, Map<String, String> headers) {
        return organizationTypeRepository.findById(dto.getOrganizationTypeId())
                .switchIfEmpty(Mono.error(new NotFoundException("OrganizationType Id - ".concat(String.valueOf(dto.getOrganizationTypeId())).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .flatMap(orgType -> outletTypeRepository.findByDealerName(dto.getDealerName().trim())
                        .flatMap(outletTypeEntity -> Mono.<WsResponse>error(new DuplicateRecordException("Outlet Type Name '" + dto.getDealerName() + "' already exists in the '" + orgType.getName() + "' Organization", ERR_CONFLICT)))
                        .switchIfEmpty(Mono.defer(() -> saveOutletType(dto, headers)))
                        .onErrorResume(throwable -> {
                                    if (returnChainedException(throwable)) {
                                        return Mono.error(throwable);
                                    }
                                    return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                                }
                        )
                )
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    private Mono<WsResponse> saveOutletType(OutletTypeDto dto, Map<String, String> headers) {
        OutletTypeEntity entity = OutletTypeEntity.builder()
                .dealerName(dto.getDealerName().trim())
                .description(dto.getDescription().trim())
                .organizationTypeId(dto.getOrganizationTypeId())
                .created(LocalDateTime.now())
                .createdBy(headers.get(X_IDENTITY))
                .build();
        return outletTypeRepository.save(entity)
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_CREATED, organizationTypeResponse, TRANS_POST_OUTLET_CREATION, FALSE, headers))
                .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_WRITE)));
    }

    @Override
    public Mono<WsResponse> update(final Long outletTypeId, final OutletTypeDto dto, Map<String, String> headers) {
        return outletTypeRepository.findById(outletTypeId)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType Id - ".concat(String.valueOf(outletTypeId)).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .flatMap(existingOutletTypeEntity -> organizationTypeRepository.findById(dto.getOrganizationTypeId()) // Check if submitted orgId exists
                        .switchIfEmpty(Mono.error(new NotFoundException("OrganizationType Id - ".concat(String.valueOf(dto.getOrganizationTypeId())).concat(NOT_FOUND), ERR_NOT_FOUND)))
                        .flatMap(orgType -> {
                            Long existingOrgId = existingOutletTypeEntity.getOrganizationTypeId();
                            Long submittedOrgId = dto.getOrganizationTypeId();
                            if (!existingOrgId.equals(submittedOrgId)) { // Check if orgId mapped to submitted outlet type is different from submitted orgId
                                // Org id mapped on outlet type is different from submitted orgId. Check if dealer name exists in submitted orgId
                                return Mono.error(new ForbiddenException("OrganizationType Id '" + existingOrgId + "' mapped on submitted Outlet Type is different from the submitted OrganizationType Id '" + submittedOrgId + "'", ERR_NOT_PERMITTED));
                            }
                            return updateOutletType(dto, existingOutletTypeEntity, headers);
                        })
                        .onErrorResume(throwable -> {
                                    if (returnChainedException(throwable)) {
                                        return Mono.error(throwable);
                                    }
                                    return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                                }
                        )

                )
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    private Mono<WsResponse> updateOutletType(OutletTypeDto dto, OutletTypeEntity entity, Map<String, String> headers) {
        LogMapper.INSTANCE.mapOutletType(dto, entity);
        entity.setUpdatedBy(headers.get(X_IDENTITY));
        entity.setUpdated(LocalDateTime.now());
        return outletTypeRepository.save(entity)
                .map(this::toResponse)
                .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeResponse, TRANS_PUT_OUTLET_UPDATE, FALSE, headers))
                .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_UPDATE)));
    }

    @Override
    public Mono<WsResponse> delete(final Long outletTypeId, Map<String, String> headers) {
        //Ensure the record by submitted id exists
        return outletTypeRepository.findById(outletTypeId)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType id - ".concat(String.valueOf(outletTypeId)).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .flatMap(existing -> outletTypeRepository.deleteById(outletTypeId)
                        .doOnSuccess(v -> log.info("Deleted OutletType id={}", outletTypeId))
                        .then(responseMapper.setApiResponse(ERR_SUCCESS, NULL, TRANS_DEL_OUTLET_DELETE, FALSE, headers))
                        .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_DELETE)))
                )
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getById(final Long outletTypeId, Map<String, String> headers) {
        return outletTypeRepository.findById(outletTypeId)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType id - ".concat(String.valueOf(outletTypeId)).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .map(this::toResponse)
                .flatMap(outletTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, outletTypeResponse, TRANS_GET_OUTLET, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getAll(Map<String, String> headers) {
        return outletTypeRepository.findAll()
                .switchIfEmpty(Mono.error(new NotFoundException("OutletTypes is empty", ERR_NOT_FOUND)))
                .map(this::toResponse)
                .collectList()
                .flatMap(outletTypeResponses -> responseMapper.setApiResponse(ERR_SUCCESS, outletTypeResponses, TRANS_GET_OUTLET, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> getByOrganizationTypeId(final Long organizationTypeId, Map<String, String> headers) {
        return outletTypeRepository.findByOrganizationTypeId(organizationTypeId)
                .switchIfEmpty(Mono.error(new NotFoundException("OutletTypes not found", ERR_NOT_FOUND)))
                .map(this::toResponse)
                .collectList()
                .flatMap(outletTypeResponses -> responseMapper.setApiResponse(ERR_SUCCESS, outletTypeResponses, TRANS_GET_OUTLET, FALSE, headers))
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    @Override
    public Mono<WsResponse> transferOut(OutletTypeTransferDto outletTypeTransferDto, Map<String, String> headers) {
        return outletTypeRepository.findById(outletTypeTransferDto.getOutletTypeId())
                .switchIfEmpty(Mono.error(new NotFoundException("OutletType id - ".concat(String.valueOf(outletTypeTransferDto.getOutletTypeId())).concat(NOT_FOUND), ERR_NOT_FOUND)))
                .flatMap(outletTypeEntity -> organizationTypeRepository.findById(outletTypeTransferDto.getTargetOrganizationTypeId())
                        .switchIfEmpty(Mono.error(new NotFoundException("OrganizationType Id - ".concat(String.valueOf(outletTypeTransferDto.getTargetOrganizationTypeId())).concat(NOT_FOUND), ERR_NOT_FOUND)))
                        .flatMap(targetOrgType -> {
                            outletTypeEntity.setOrganizationTypeId(outletTypeTransferDto.getTargetOrganizationTypeId());
                            outletTypeEntity.setUpdatedBy(headers.get(X_IDENTITY));
                            outletTypeEntity.setUpdated(LocalDateTime.now());
                            return outletTypeRepository.save(outletTypeEntity)
                                    .map(this::toResponse)
                                    .flatMap(organizationTypeResponse -> responseMapper.setApiResponse(ERR_SUCCESS, organizationTypeResponse, TRANS_PUT_OUTLET_TRANSFER, FALSE, headers))
                                    .onErrorResume(throwable -> Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_UPDATE)));
                        })
                        .onErrorResume(throwable -> {
                                    if (returnChainedException(throwable)) {
                                        return Mono.error(throwable);
                                    }
                                    return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                                }
                        )
                )
                .onErrorResume(throwable -> {
                            if (returnChainedException(throwable)) {
                                return Mono.error(throwable);
                            }
                            return Mono.error(new InternalServerErrorException(throwable.getLocalizedMessage(), ERR_DB_READ));
                        }
                );
    }

    private OutletTypeResponse toResponse(OutletTypeEntity entity) {
        return OutletTypeResponse.builder()
                .id(entity.getId())
                .dealerName(entity.getDealerName())
                .description(entity.getDescription())
                .organizationTypeId(entity.getOrganizationTypeId())
                .build();
    }
}
