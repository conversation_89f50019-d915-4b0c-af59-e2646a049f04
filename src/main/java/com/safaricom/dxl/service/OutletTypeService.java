package com.safaricom.dxl.service;

import com.safaricom.dxl.data.dto.OutletTypeDto;
import com.safaricom.dxl.data.dto.OutletTypeTransferDto;
import com.safaricom.dxl.webflux.starter.model.WsResponse;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * Service interface for managing Outlet Types.
 */
public interface OutletTypeService {
    Mono<WsResponse> create(OutletTypeDto dto, Map<String, String> headers);
    Mono<WsResponse> update(Long outletTypeId, OutletTypeDto dto, Map<String, String> headers);
    Mono<WsResponse> delete(Long outletTypeId, Map<String, String> headers);
    Mono<WsResponse> getById(Long outletTypeId, Map<String, String> headers);
    Mono<WsResponse> getAll(Map<String, String> headers);
    Mono<WsResponse> getByOrganizationTypeId(Long organizationTypeId, Map<String, String> headers);
    Mono<WsResponse> transferOut(OutletTypeTransferDto outletTypeTransferDto, Map<String, String> headers);
}
