package com.safaricom.dxl.data.repositories;

import com.safaricom.dxl.data.entities.OutletTypeEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.r2dbc.repository.R2dbcRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface OutletTypeRepository extends R2dbcRepository<OutletTypeEntity, Long> {

    @Query(value = "SELECT * FROM dealer.pp_outlet_type WHERE pp_outlet_type.organization_type_id = :o")
    Flux<OutletTypeEntity> findByOrganizationTypeId(@Param("o") Long organizationTypeId);

    @Query(value = "SELECT * FROM dealer.pp_outlet_type WHERE pp_outlet_type.dealer_name = :d")
    Mono<OutletTypeEntity> findByDealerName(@Param("d") String dealerName);
}
